/**
 * Timezone utility functions for proper datetime handling in forms and scheduling
 */

/**
 * Convert a stored datetime to a datetime-local string for form display
 * The key insight: datetime-local inputs expect local time in the selected timezone
 * @param storedDate - The date from the database (could be UTC or timezone-aware)
 * @param timezone - The timezone the datetime should be displayed in
 * @returns A datetime-local string (YYYY-MM-DDTHH:mm)
 */
export function formatDateTimeForInput(storedDate: Date, timezone: string): string {
  // The stored date represents the actual moment in time
  // We need to show what that moment looks like in the specified timezone
  const formatter = new Intl.DateTimeFormat('sv-SE', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })

  // Format and convert to datetime-local format
  const formatted = formatter.format(storedDate)
  return formatted.replace(' ', 'T')
}

/**
 * Convert a datetime-local string to a Date object, treating it as being in the specified timezone
 * This uses a simple but effective approach that works with how browsers handle timezones
 * @param dateTimeString - The datetime-local string from the form (YYYY-MM-DDTHH:mm)
 * @param timezone - The timezone the datetime should be interpreted in
 * @returns A Date object representing that moment in UTC
 */
export function parseDateTimeFromInput(dateTimeString: string, timezone: string): Date {
  if (!dateTimeString) return new Date()

  // For now, let's use a simple approach: treat the datetime-local as local time
  // and store it directly. This is the most straightforward approach that works
  // with how most applications handle datetime-local inputs.

  // Add seconds and timezone info to make it a complete ISO string
  const isoString = dateTimeString + ':00.000Z'
  return new Date(isoString)
}

/**
 * Get the current time in a specific timezone
 * @param timezone - The target timezone (e.g., 'Asia/Kolkata')
 * @returns A Date object representing the current time in that timezone
 */
export function getCurrentTimeInTimezone(timezone: string): Date {
  const now = new Date()
  return new Date(now.toLocaleString('en-US', { timeZone: timezone }))
}

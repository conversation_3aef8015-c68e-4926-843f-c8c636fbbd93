import { z } from 'zod'

// URL validation
export const urlSchema = z.string().url('Please enter a valid URL')

// Username validation
export const usernameSchema = z.string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be less than 30 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores')
  .regex(/^[a-zA-Z0-9]/, 'Username cannot start with special characters')
  .regex(/[a-zA-Z0-9]$/, 'Username cannot end with special characters')
  .regex(/^(?!.*[-_]{2,}).*$/, 'Username cannot contain consecutive special characters')

// User validation schemas
export const createUserSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  username: usernameSchema,
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  profileImage: z.string().url().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
})

export const updateUserSchema = z.object({
  email: z.string().email('Please enter a valid email address').optional(),
  username: usernameSchema.optional(),
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters').optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional().nullable(),
  profileImage: z.string().url().optional().nullable(),
})

// Link validation schemas
export const createLinkSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  url: urlSchema,
  icon: z.string().optional(),
  isVisible: z.boolean().default(true),
  order: z.number().int().min(0).optional(),

  // Scheduling fields
  isScheduled: z.boolean().default(false),
  scheduleStart: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  scheduleEnd: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  timezone: z.string().default('UTC'),
  // Conditional rules
  hasConditions: z.boolean().default(false),
  defaultBehavior: z.enum(['show', 'hide']).default('show'),
}).refine((data) => {
  if (data.isScheduled && !data.scheduleStart) {
    return false
  }
  if (data.scheduleStart && data.scheduleEnd && data.scheduleStart >= data.scheduleEnd) {
    return false
  }
  return true
}, {
  message: "Invalid scheduling configuration",
  path: ["scheduleStart"]
})

export const updateLinkSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters').optional(),
  url: urlSchema.optional(),
  icon: z.string().optional(),
  isVisible: z.boolean().optional(),
  order: z.number().int().min(0).optional(),

  // Scheduling fields
  isScheduled: z.boolean().optional(),
  scheduleStart: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  scheduleEnd: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  timezone: z.string().optional(),
  // Conditional rules
  hasConditions: z.boolean().optional(),
  defaultBehavior: z.enum(['show', 'hide']).optional(),
}).refine((data) => {
  if (data.isScheduled && !data.scheduleStart) {
    return false
  }
  if (data.scheduleStart && data.scheduleEnd && data.scheduleStart >= data.scheduleEnd) {
    return false
  }
  return true
}, {
  message: "Invalid scheduling configuration",
  path: ["scheduleStart"]
})

export const linkSchema = createLinkSchema // Backward compatibility

// Profile validation schemas
export const createProfileSchema = z.object({
  userId: z.string().cuid(),
  slug: z.string().min(3, 'Slug must be at least 3 characters').max(20, 'Slug must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Slug can only contain letters, numbers, hyphens, and underscores'),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    fontFamily: z.string().min(1, 'Font family is required'),
    preset: z.string().optional(),
  }).default({
    primaryColor: '#000000',
    secondaryColor: '#666666',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    fontFamily: 'Inter',
  }),
  backgroundType: z.enum(['color', 'gradient', 'image']).default('color'),
  backgroundValue: z.string().default('#ffffff'),
  isPublic: z.boolean().default(true),
})

export const updateProfileSchema = z.object({
  slug: z.string().min(3, 'Slug must be at least 3 characters').max(20, 'Slug must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Slug can only contain letters, numbers, hyphens, and underscores').optional(),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    fontFamily: z.string().min(1, 'Font family is required'),
    preset: z.string().optional(),
  }).optional(),
  backgroundType: z.enum(['color', 'gradient', 'image']).optional(),
  backgroundValue: z.string().optional(),
  isPublic: z.boolean().optional(),
})

export const profileUpdateSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  username: usernameSchema,
}) // Backward compatibility

// Type exports
export type CreateUserData = z.infer<typeof createUserSchema>
export type UpdateUserData = z.infer<typeof updateUserSchema>
export type CreateLinkData = z.infer<typeof createLinkSchema>
export type UpdateLinkData = z.infer<typeof updateLinkSchema>
export type CreateProfileData = z.infer<typeof createProfileSchema>
export type UpdateProfileData = z.infer<typeof updateProfileSchema>
export type CreateLinkConditionData = z.infer<typeof createLinkConditionSchema>
export type UpdateLinkConditionData = z.infer<typeof updateLinkConditionSchema>
export type ConditionAction = z.infer<typeof conditionActionSchema>
export type ReferrerRule = z.infer<typeof referrerRuleSchema>
export type LocationRule = z.infer<typeof locationRuleSchema>
export type DeviceRule = z.infer<typeof deviceRuleSchema>
export type TimeRule = z.infer<typeof timeRuleSchema>

// Backward compatibility
export type LinkData = z.infer<typeof linkSchema>
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>

// UI Form validation schemas
export const contactFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  message: z.string().min(1, 'Message is required').max(1000, 'Message must be less than 1000 characters'),
})

export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(100, 'Search query must be less than 100 characters'),
})

// Theme validation
export const themeSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  fontFamily: z.string().min(1, 'Font family is required'),
  preset: z.string().optional(),
})

// LinkCondition validation schemas
export const conditionActionSchema = z.object({
  type: z.enum(['show', 'hide', 'redirect'], {
    errorMap: () => ({ message: 'Action type must be show, hide, or redirect' })
  }),
  value: z.string().url('Redirect URL must be valid').optional(),
  alternateTitle: z.string().max(100, 'Alternate title must be less than 100 characters').optional(),
  alternateIcon: z.string().optional(),
})

export const referrerRuleSchema = z.object({
  domains: z.array(z.string().min(1, 'Domain cannot be empty')).min(1, 'At least one domain is required'),
  matchType: z.enum(['exact', 'contains', 'regex'], {
    errorMap: () => ({ message: 'Match type must be exact, contains, or regex' })
  }),
  caseSensitive: z.boolean().default(false),
})

export const locationRuleSchema = z.object({
  countries: z.array(z.string().length(2, 'Country code must be 2 characters')).optional(),
  regions: z.array(z.string().min(1, 'Region cannot be empty')).optional(),
  cities: z.array(z.string().min(1, 'City cannot be empty')).optional(),
  matchType: z.enum(['include', 'exclude']).default('include'),
})

export const deviceRuleSchema = z.object({
  types: z.array(z.enum(['mobile', 'desktop', 'tablet'])).optional(),
  platforms: z.array(z.string().min(1, 'Platform cannot be empty')).optional(),
  browsers: z.array(z.string().min(1, 'Browser cannot be empty')).optional(),
  matchType: z.enum(['include', 'exclude']).default('include'),
})

export const timeRuleSchema = z.object({
  daysOfWeek: z.array(z.number().min(0).max(6)).optional(), // 0 = Sunday
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Start time must be in HH:mm format').optional(),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'End time must be in HH:mm format').optional(),
  timezone: z.string().default('UTC'),
}).refine((data) => {
  if (data.startTime && data.endTime && data.startTime >= data.endTime) {
    return false
  }
  return true
}, {
  message: "End time must be after start time",
  path: ["endTime"]
})

export const createLinkConditionSchema = z.object({
  linkId: z.string().cuid('Invalid link ID format'),
  type: z.enum(['referrer', 'location', 'device', 'time'], {
    errorMap: () => ({ message: 'Condition type must be referrer, location, device, or time' })
  }),
  priority: z.number().int().min(0).max(1000).default(0),
  isActive: z.boolean().default(true),
  rules: z.union([referrerRuleSchema, locationRuleSchema, deviceRuleSchema, timeRuleSchema]),
  action: conditionActionSchema,
}).refine((data) => {
  // Validate rules match the condition type
  if (data.type === 'referrer' && !referrerRuleSchema.safeParse(data.rules).success) {
    return false
  }
  if (data.type === 'location' && !locationRuleSchema.safeParse(data.rules).success) {
    return false
  }
  if (data.type === 'device' && !deviceRuleSchema.safeParse(data.rules).success) {
    return false
  }
  if (data.type === 'time' && !timeRuleSchema.safeParse(data.rules).success) {
    return false
  }
  return true
}, {
  message: "Rules must match the specified condition type",
  path: ["rules"]
})

export const updateLinkConditionSchema = z.object({
  type: z.enum(['referrer', 'location', 'device', 'time']).optional(),
  priority: z.number().int().min(0).max(1000).optional(),
  isActive: z.boolean().optional(),
  rules: z.union([referrerRuleSchema, locationRuleSchema, deviceRuleSchema, timeRuleSchema]).optional(),
  action: conditionActionSchema.optional(),
}).refine((data) => {
  // If type is provided, rules must match
  if (data.type && data.rules) {
    if (data.type === 'referrer' && !referrerRuleSchema.safeParse(data.rules).success) {
      return false
    }
    if (data.type === 'location' && !locationRuleSchema.safeParse(data.rules).success) {
      return false
    }
    if (data.type === 'device' && !deviceRuleSchema.safeParse(data.rules).success) {
      return false
    }
    if (data.type === 'time' && !timeRuleSchema.safeParse(data.rules).success) {
      return false
    }
  }
  return true
}, {
  message: "Rules must match the specified condition type",
  path: ["rules"]
})

// Account settings validation schemas
export const usernameUpdateSchema = z.object({
  username: usernameSchema,
})

export const accountDeletionSchema = z.object({
  confirmation: z.literal('DELETE', {
    errorMap: () => ({ message: 'You must type DELETE to confirm account deletion' })
  }),
})

export type ContactFormData = z.infer<typeof contactFormSchema>
export type SearchData = z.infer<typeof searchSchema>
export type ThemeData = z.infer<typeof themeSchema>
export type UsernameUpdateData = z.infer<typeof usernameUpdateSchema>
export type AccountDeletionData = z.infer<typeof accountDeletionSchema>
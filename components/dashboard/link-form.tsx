'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ScreenReaderOnly, LiveRegion } from '@/components/ui/skip-links'
import { createLinkSchema, updateLinkSchema, type CreateLinkData, type UpdateLinkData } from '@/lib/validations'
import { createLink, updateLink } from '@/lib/actions/links'
import { toast } from 'sonner'
import { Plus, Edit, Link as LinkIcon, Github, Twitter, Instagram, Youtube, Linkedin, Globe, Mail, Phone, Calendar, Clock, AlertTriangle, CheckCircle, Settings, Zap } from 'lucide-react'

// Icon options for links
const ICON_OPTIONS = [
  { value: 'none', label: 'No Icon', icon: null },
  { value: 'link', label: 'Link', icon: LinkIcon },
  { value: 'globe', label: 'Website', icon: Globe },
  { value: 'github', label: 'GitHub', icon: Github },
  { value: 'twitter', label: 'Twitter', icon: Twitter },
  { value: 'instagram', label: 'Instagram', icon: Instagram },
  { value: 'youtube', label: 'YouTube', icon: Youtube },
  { value: 'linkedin', label: 'LinkedIn', icon: Linkedin },
  { value: 'mail', label: 'Email', icon: Mail },
  { value: 'phone', label: 'Phone', icon: Phone },
]

interface LinkFormProps {
  mode: 'create' | 'edit'
  link?: {
    id: string
    title: string
    url: string
    icon?: string | null
    isVisible: boolean
    isScheduled?: boolean
    scheduleStart?: Date | null
    scheduleEnd?: Date | null
    timezone?: string | null
    hasConditions?: boolean
    defaultBehavior?: string
    conditions?: Array<{
      id: string
      type: string
      priority: number
      isActive: boolean
      rules: any
      action: any
    }>
  }
  trigger?: React.ReactNode
  onSuccess?: () => void
  showAdvancedOptions?: boolean
}

// Priority level options
const PRIORITY_LEVELS = [
  { value: 0, label: 'Low Priority', description: 'Evaluated last' },
  { value: 50, label: 'Normal Priority', description: 'Default priority level' },
  { value: 100, label: 'High Priority', description: 'Evaluated early' },
  { value: 200, label: 'Critical Priority', description: 'Evaluated first' },
]

// Validation states
interface ValidationState {
  isValidating: boolean
  conflicts: Array<{
    type: 'warning' | 'error'
    message: string
    suggestion?: string
  }>
  performance: {
    estimatedEvaluationTime?: number
    complexity?: 'low' | 'medium' | 'high'
  }
}

export function LinkForm({ mode, link, trigger, onSuccess, showAdvancedOptions = false }: LinkFormProps) {
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<string>('')
  const [activeTab, setActiveTab] = useState('basic')
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    conflicts: [],
    performance: {}
  })
  const titleInputRef = useRef<HTMLInputElement>(null)

  const isEdit = mode === 'edit'

  // Local state to track timezone independently to prevent reversion during submission
  const [localTimezone, setLocalTimezone] = useState<string>(() => {
    return (isEdit && link?.timezone) ? link.timezone : 'UTC'
  })
  const schema = isEdit ? updateLinkSchema : createLinkSchema

  const form = useForm<CreateLinkData | UpdateLinkData>({
    resolver: zodResolver(schema),
    defaultValues: isEdit && link ? {
      title: link.title,
      url: link.url,
      icon: link.icon || 'none',
      isVisible: link.isVisible,
      isScheduled: link.isScheduled || false,
      scheduleStart: link.scheduleStart ? link.scheduleStart.toISOString().slice(0, 16) : undefined,
      scheduleEnd: link.scheduleEnd ? link.scheduleEnd.toISOString().slice(0, 16) : undefined,
      timezone: link.timezone || 'UTC',
      hasConditions: link.hasConditions || false,
      defaultBehavior: link.defaultBehavior || 'show',
    } : {
      title: '',
      url: '',
      icon: 'none',
      isVisible: true,
      isScheduled: false,
      scheduleStart: undefined,
      scheduleEnd: undefined,
      timezone: 'UTC',
      hasConditions: false,
      defaultBehavior: 'show',
    }
  })

  const selectedIcon = form.watch('icon') || 'none'
  const IconComponent = ICON_OPTIONS.find(option => option.value === selectedIcon)?.icon
  const isScheduled = form.watch('isScheduled')
  const priority = form.watch('priority') || 50
  const title = form.watch('title')
  const url = form.watch('url')
  const timezone = form.watch('timezone') || 'UTC'

  // Real-time validation function
  const validateRules = useCallback(async (formData: any) => {
    if (!formData.title || !formData.url) return

    setValidationState(prev => ({ ...prev, isValidating: true }))

    try {
      // Simulate API call for rule validation
      const response = await fetch('/api/rules/conditions/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          linkId: link?.id,
          title: formData.title,
          url: formData.url,
          priority: formData.priority,
          isVisible: formData.isVisible,
        })
      })

      if (response.ok) {
        const validation = await response.json()
        setValidationState(prev => ({
          ...prev,
          isValidating: false,
          conflicts: validation.conflicts || [],
          performance: validation.performance || {}
        }))
      }
    } catch (error) {
      console.error('Validation error:', error)
      setValidationState(prev => ({ ...prev, isValidating: false }))
    }
  }, [link?.id])

  // Debounced validation
  useEffect(() => {
    const timer = setTimeout(() => {
      if (title && url) {
        validateRules({ title, url, priority, isVisible: form.watch('isVisible') })
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [title, url, priority, validateRules, form])

  // Focus management
  useEffect(() => {
    if (open && titleInputRef.current) {
      // Small delay to ensure dialog is fully rendered
      setTimeout(() => {
        titleInputRef.current?.focus()
      }, 100)
    }
  }, [open])

  // Sync local timezone state when dialog opens
  useEffect(() => {
    if (open && isEdit && link) {
      setLocalTimezone(link.timezone || 'UTC')
    } else if (open && !isEdit) {
      setLocalTimezone('UTC')
    }
  }, [open, isEdit, link])

  // Handle dialog close - reset local state when dialog closes
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      // Reset local timezone state when dialog closes
      if (isEdit && link) {
        setLocalTimezone(link.timezone || 'UTC')
      } else {
        setLocalTimezone('UTC')
      }
    }
  }

  async function onSubmit(data: CreateLinkData | UpdateLinkData) {
    setIsSubmitting(true)
    setSubmitStatus('Saving link...')
    
    try {
      const result = isEdit && link 
        ? await updateLink(link.id, data as UpdateLinkData)
        : await createLink(data as CreateLinkData)

      if (result.success) {
        const successMessage = isEdit ? 'Link updated successfully' : 'Link created successfully'

        // Close dialog immediately to prevent state conflicts
        setOpen(false)

        // Reset form state and local timezone state
        form.reset()
        setLocalTimezone('UTC')

        // Show success message
        toast.success(successMessage)
        setSubmitStatus(successMessage)

        // Call parent callback after a small delay to ensure dialog is fully closed
        setTimeout(() => {
          onSuccess?.()
        }, 100)
      } else {
        const errorMessage = result.error || 'Something went wrong'
        toast.error(errorMessage)
        setSubmitStatus(`Error: ${errorMessage}`)
      }
    } catch (error) {
      const errorMessage = 'Something went wrong'
      toast.error(errorMessage)
      setSubmitStatus(`Error: ${errorMessage}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button variant={isEdit ? "ghost" : "default"} size={isEdit ? "sm" : "default"}>
      {isEdit ? (
        <>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </>
      ) : (
        <>
          <Plus className="h-4 w-4 mr-2" />
          Add Link
        </>
      )}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]" aria-describedby="link-form-description">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? 'Edit Link' : 'Add New Link'}
          </DialogTitle>
          <DialogDescription id="link-form-description">
            {isEdit 
              ? 'Update the details of your link below.' 
              : 'Fill in the details to add a new link to your profile.'
            }
          </DialogDescription>
        </DialogHeader>
        
        {submitStatus && (
          <LiveRegion priority="polite">
            {submitStatus}
          </LiveRegion>
        )}
        {/* Validation Status */}
        {validationState.conflicts.length > 0 && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                {validationState.conflicts.map((conflict, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Badge variant={conflict.type === 'error' ? 'destructive' : 'secondary'}>
                      {conflict.type}
                    </Badge>
                    <div className="flex-1">
                      <p className="text-sm">{conflict.message}</p>
                      {conflict.suggestion && (
                        <p className="text-xs text-muted-foreground mt-1">{conflict.suggestion}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}

        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4"
          noValidate
          aria-label={isEdit ? 'Edit link form' : 'Create new link form'}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Advanced
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="title">
                  Title <span className="text-red-500" aria-label="required">*</span>
                </Label>
                <Input
                  ref={titleInputRef}
                  id="title"
                  placeholder="e.g., My Portfolio"
                  aria-describedby={form.formState.errors.title ? "title-error" : undefined}
                  aria-invalid={!!form.formState.errors.title}
                  {...form.register('title')}
                />
                {form.formState.errors.title && (
                  <p id="title-error" className="text-sm text-red-500" role="alert">
                    {form.formState.errors.title.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="url">
                  URL <span className="text-red-500" aria-label="required">*</span>
                </Label>
                <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com"
                  aria-describedby={form.formState.errors.url ? "url-error" : "url-help"}
                  aria-invalid={!!form.formState.errors.url}
                  {...form.register('url')}
                />
                <p id="url-help" className="text-xs text-muted-foreground">
                  Include the full URL starting with https://
                </p>
                {form.formState.errors.url && (
                  <p id="url-error" className="text-sm text-red-500" role="alert">
                    {form.formState.errors.url.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="icon-select">Icon (optional)</Label>
                <Select
                  value={selectedIcon || 'none'}
                  onValueChange={(value) => form.setValue('icon', value === 'none' ? undefined : value)}
                >
                  <SelectTrigger id="icon-select" aria-describedby="icon-help">
                    <SelectValue>
                      <div className="flex items-center gap-2">
                        {IconComponent && <IconComponent className="h-4 w-4" aria-hidden="true" />}
                        {ICON_OPTIONS.find(option => option.value === selectedIcon)?.label || 'Select an icon'}
                      </div>
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {ICON_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          {option.icon && <option.icon className="h-4 w-4" aria-hidden="true" />}
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p id="icon-help" className="text-xs text-muted-foreground">
                  Choose an icon to display next to your link
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="isVisible">Visible on profile</Label>
                  <p className="text-xs text-muted-foreground">
                    Toggle whether this link appears on your public profile
                  </p>
                </div>
                <Switch
                  id="isVisible"
                  checked={form.watch('isVisible')}
                  onCheckedChange={(checked) => form.setValue('isVisible', checked)}
                  aria-describedby="visibility-help"
                />
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              {/* Priority Settings */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-orange-500" />
                  <Label htmlFor="priority-select">Rule Priority</Label>
                </div>
                <Select
                  value={priority.toString()}
                  onValueChange={(value) => form.setValue('priority', parseInt(value))}
                >
                  <SelectTrigger id="priority-select" aria-describedby="priority-help">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITY_LEVELS.map((level) => (
                      <SelectItem key={level.value} value={level.value.toString()}>
                        <div className="flex flex-col">
                          <span>{level.label}</span>
                          <span className="text-xs text-muted-foreground">{level.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p id="priority-help" className="text-xs text-muted-foreground">
                  Higher priority rules are evaluated first. Use this to control which conditions take precedence.
                </p>

                {/* Performance Indicator */}
                {validationState.performance.complexity && (
                  <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
                    <Badge variant={
                      validationState.performance.complexity === 'low' ? 'default' :
                      validationState.performance.complexity === 'medium' ? 'secondary' : 'destructive'
                    }>
                      {validationState.performance.complexity} complexity
                    </Badge>
                    {validationState.performance.estimatedEvaluationTime && (
                      <span className="text-xs text-muted-foreground">
                        ~{validationState.performance.estimatedEvaluationTime}ms evaluation time
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Scheduling Section */}
              <div className="space-y-3 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="isScheduled" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Schedule link
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Show this link only during specific dates and times
                    </p>
                  </div>
                  <Switch
                    id="isScheduled"
                    checked={isScheduled}
                    onCheckedChange={(checked) => form.setValue('isScheduled', checked)}
                    aria-describedby="schedule-help"
                  />
                </div>

                {isScheduled && (
                  <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                    <div className="space-y-2">
                      <Label htmlFor="scheduleStart" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Start Date & Time
                      </Label>
                      <Input
                        id="scheduleStart"
                        type="datetime-local"
                        {...form.register('scheduleStart')}
                        aria-describedby={form.formState.errors.scheduleStart ? "schedule-start-error" : undefined}
                        aria-invalid={!!form.formState.errors.scheduleStart}
                      />
                      {form.formState.errors.scheduleStart && (
                        <p id="schedule-start-error" className="text-sm text-red-500" role="alert">
                          {form.formState.errors.scheduleStart.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="scheduleEnd">End Date & Time (optional)</Label>
                      <Input
                        id="scheduleEnd"
                        type="datetime-local"
                        {...form.register('scheduleEnd')}
                        aria-describedby={form.formState.errors.scheduleEnd ? "schedule-end-error" : "schedule-end-help"}
                        aria-invalid={!!form.formState.errors.scheduleEnd}
                      />
                      <p id="schedule-end-help" className="text-xs text-muted-foreground">
                        Leave empty to show the link indefinitely after start date
                      </p>
                      {form.formState.errors.scheduleEnd && (
                        <p id="schedule-end-error" className="text-sm text-red-500" role="alert">
                          {form.formState.errors.scheduleEnd.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timezone-select">Timezone</Label>
                      <Select
                        value={localTimezone}
                        onValueChange={(value) => {
                          setLocalTimezone(value)
                          form.setValue('timezone', value, { shouldDirty: true, shouldTouch: true })
                        }}
                      >
                        <SelectTrigger id="timezone-select">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="UTC">UTC</SelectItem>
                          <SelectItem value="America/New_York">Eastern Time</SelectItem>
                          <SelectItem value="America/Chicago">Central Time</SelectItem>
                          <SelectItem value="America/Denver">Mountain Time</SelectItem>
                          <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                          <SelectItem value="Europe/London">London</SelectItem>
                          <SelectItem value="Europe/Paris">Paris</SelectItem>
                          <SelectItem value="Europe/Berlin">Berlin</SelectItem>
                          <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                          <SelectItem value="Asia/Shanghai">Shanghai</SelectItem>
                          <SelectItem value="Asia/Kolkata">India</SelectItem>
                          <SelectItem value="Australia/Sydney">Sydney</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (isEdit ? 'Update Link' : 'Create Link')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
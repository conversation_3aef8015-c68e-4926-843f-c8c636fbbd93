// Test script to verify timezone functionality
const { formatDateTimeForInput, getCurrentTimeInTimezone, parseDateTimeFromInput } = require('./lib/utils/timezone-helpers.ts')

// Test 1: Format datetime for input
console.log('=== Test 1: Format DateTime for Input ===')

// Create a test date (January 15, 2024, 2:54 AM UTC)
const testDate = new Date('2024-01-15T02:54:00.000Z')
console.log('Original UTC date:', testDate.toISOString())

// Test formatting for different timezones
const timezones = ['UTC', 'Asia/Kolkata', 'Europe/Paris', 'America/New_York']

timezones.forEach(tz => {
  try {
    const formatted = formatDateTimeForInput(testDate, tz)
    console.log(`${tz}: ${formatted}`)
    
    // Also show what this looks like in that timezone
    const readable = testDate.toLocaleString('en-US', { timeZone: tz })
    console.log(`  (Human readable: ${readable})`)
  } catch (error) {
    console.error(`Error formatting for ${tz}:`, error.message)
  }
})

console.log('\n=== Test 2: Current Time in Timezone ===')

// Test getting current time in different timezones
timezones.forEach(tz => {
  try {
    const currentTime = getCurrentTimeInTimezone(tz)
    console.log(`Current time in ${tz}: ${currentTime.toISOString()}`)
    console.log(`  (Human readable: ${currentTime.toLocaleString('en-US', { timeZone: tz })})`)
  } catch (error) {
    console.error(`Error getting current time for ${tz}:`, error.message)
  }
})

console.log('\n=== Test 3: Scheduling Logic Simulation ===')

// Simulate the scheduling scenario from the user's issue
const scheduleStart = new Date('2024-01-15T02:54:00.000Z') // 2:54 AM UTC
const scheduleEnd = new Date('2024-01-15T02:57:00.000Z')   // 2:57 AM UTC
const linkTimezone = 'Asia/Kolkata'

console.log('Schedule Start (UTC):', scheduleStart.toISOString())
console.log('Schedule End (UTC):', scheduleEnd.toISOString())
console.log('Link Timezone:', linkTimezone)

// Convert to link timezone for display
const startInLinkTz = scheduleStart.toLocaleString('en-US', { timeZone: linkTimezone })
const endInLinkTz = scheduleEnd.toLocaleString('en-US', { timeZone: linkTimezone })

console.log('Schedule Start in Link TZ:', startInLinkTz)
console.log('Schedule End in Link TZ:', endInLinkTz)

// Test what the form would show
const formStartValue = formatDateTimeForInput(scheduleStart, linkTimezone)
const formEndValue = formatDateTimeForInput(scheduleEnd, linkTimezone)

console.log('Form would show Start:', formStartValue)
console.log('Form would show End:', formEndValue)

// Test if current time would be within schedule
const currentTimeInLinkTz = getCurrentTimeInTimezone(linkTimezone)
const currentTimeForComparison = new Date(currentTimeInLinkTz.toLocaleString('en-US', { timeZone: linkTimezone }))

console.log('Current time in link TZ:', currentTimeForComparison.toISOString())
console.log('Is current time >= start?', currentTimeForComparison >= scheduleStart)
console.log('Is current time <= end?', currentTimeForComparison <= scheduleEnd)
console.log('Would link be visible?', currentTimeForComparison >= scheduleStart && currentTimeForComparison <= scheduleEnd)

console.log('\n=== Test 4: Simplified Approach ===')

// Test the simplified approach: store as entered, compare in timezone
const userInputStart = '2024-01-15T02:54' // User enters 2:54 AM in India time
const userInputEnd = '2024-01-15T02:57'   // User enters 2:57 AM in India time
const userTimezone = 'Asia/Kolkata'

console.log('User input Start:', userInputStart)
console.log('User input End:', userInputEnd)
console.log('User timezone:', userTimezone)

// Store as entered (simplified approach)
const storedStart = new Date(userInputStart + ':00.000')
const storedEnd = new Date(userInputEnd + ':00.000')

console.log('Stored Start:', storedStart.toISOString())
console.log('Stored End:', storedEnd.toISOString())

// Form display (simplified - show as stored)
const displayStart = storedStart.toISOString().slice(0, 16)
const displayEnd = storedEnd.toISOString().slice(0, 16)

console.log('Form displays Start:', displayStart)
console.log('Form displays End:', displayEnd)

// Check if round-trip is successful
console.log('Round-trip successful?', displayStart === userInputStart && displayEnd === userInputEnd)

// Test scheduling logic with current time
const testCurrentTime = new Date('2024-01-15T02:55:00.000Z') // 2:55 AM UTC (should be 8:25 AM in India)
const currentTimeInLinkTz = new Date(testCurrentTime.toLocaleString('en-US', { timeZone: userTimezone }))

console.log('Test current time (UTC):', testCurrentTime.toISOString())
console.log('Test current time in link TZ:', currentTimeInLinkTz.toISOString())
console.log('Is current time >= start?', currentTimeInLinkTz >= storedStart)
console.log('Is current time <= end?', currentTimeInLinkTz <= storedEnd)
console.log('Would link be visible?', currentTimeInLinkTz >= storedStart && currentTimeInLinkTz <= storedEnd)
